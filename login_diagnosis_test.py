"""
登录方案诊断测试工具
使用Chrome MCP工具进行HTTP登录方案的详细分析和对比测试
"""

import requests
import hashlib
import time
import base64
import json
from urllib.parse import quote, unquote
import os
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('login_diagnosis.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

# 验证码识别相关导入
try:
    import ddddocr
    DDDDOCR_AVAILABLE = True
    print("[+] ddddocr库已加载，将使用自动验证码识别")
except ImportError:
    DDDDOCR_AVAILABLE = False
    print("[-] ddddocr库未安装，将使用手动验证码输入")

class LoginDiagnosisTester:
    """登录诊断测试器"""
    
    def __init__(self):
        self.session = requests.Session()
        self.base_url = "https://login-sso.siyscrm.com"
        self.username = "***********"
        self.password = "aabb6688"
        self.client_id = "81c4e66b04066aa8da079f3e5d2bf1f3"
        self.redirect_uri = "https://apollo.siyscrm.com/account/login/loginsuccess"
        
        # 初始化验证码识别器
        self.ocr = None
        if DDDDOCR_AVAILABLE:
            try:
                self.ocr = ddddocr.DdddOcr()
                logging.info("验证码识别器初始化成功")
            except Exception as e:
                logging.error(f"验证码识别器初始化失败: {e}")
                self.ocr = None
    
    def log_request_details(self, method, url, headers=None, data=None, response=None):
        """详细记录请求信息"""
        logging.info(f"=== {method} 请求详情 ===")
        logging.info(f"URL: {url}")
        
        if headers:
            logging.info("请求头:")
            for key, value in headers.items():
                logging.info(f"  {key}: {value}")
        
        if data:
            logging.info(f"请求体: {json.dumps(data, indent=2, ensure_ascii=False) if isinstance(data, dict) else data}")
        
        if response:
            logging.info(f"响应状态码: {response.status_code}")
            logging.info("响应头:")
            for key, value in response.headers.items():
                logging.info(f"  {key}: {value}")
            
            try:
                if response.headers.get('content-type', '').startswith('application/json'):
                    logging.info(f"响应内容: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
                else:
                    logging.info(f"响应内容: {response.text[:500]}...")
            except:
                logging.info(f"响应内容: {response.text[:500]}...")
        
        logging.info("=" * 50)
    
    def fix_token_decode(self, token):
        """修复token解码函数"""
        try:
            # 第一步：URL解码
            url_decoded = unquote(token)
            # 第二步：Base64解码
            base64_decoded = base64.b64decode(url_decoded).decode('utf-8')
            # 第三步：再次URL解码
            final_decoded = unquote(base64_decoded)
            logging.info(f"Token解码结果: {final_decoded}")
            return final_decoded
        except Exception as e:
            logging.error(f"Token解码失败: {e}")
            return None
    
    def build_token_corrected(self, username, password, client_id, redirect_uri, access_code, timestamp):
        """修正的token构造函数"""
        token_data = {
            "name": username,
            "pwd": password,
            "redirect_uri": redirect_uri,
            "client_id": client_id,
            "access_code": access_code,
            "timestamp": timestamp
        }
        
        # 转换为JSON字符串（无空格）
        json_str = json.dumps(token_data, separators=(',', ':'))
        logging.info(f"Token原始数据: {json_str}")
        
        # 第一步：URL编码
        url_encoded = quote(json_str)
        logging.info(f"URL编码后: {url_encoded}")
        
        # 第二步：Base64编码
        base64_encoded = base64.b64encode(url_encoded.encode('utf-8')).decode('utf-8')
        logging.info(f"Base64编码后: {base64_encoded}")
        
        # 第三步：再次URL编码
        final_token = quote(base64_encoded)
        logging.info(f"最终Token: {final_token}")
        
        return final_token
    
    def recognize_captcha(self, image_path):
        """自动识别验证码"""
        if not self.ocr:
            return None
        
        try:
            with open(image_path, 'rb') as f:
                img_bytes = f.read()
            
            result = self.ocr.classification(img_bytes)
            # 清理结果
            cleaned = ''.join(char for char in result if char.isalnum()).upper()
            logging.info(f"验证码识别结果: 原始='{result}', 清理后='{cleaned}'")
            return cleaned if len(cleaned) >= 3 else None
        except Exception as e:
            logging.error(f"验证码识别失败: {e}")
            return None
    
    def test_step_1_login_page(self):
        """测试步骤1：访问登录页面"""
        print("\n[测试步骤1] 访问登录页面")
        
        login_page_url = f"{self.base_url}/html/apollo_v4/index.html?redirect_uri=https%3a%2f%2fapollo.siyscrm.com%2faccount%2flogin%2floginsuccess&client_id={self.client_id}"
        
        headers = {
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Encoding': 'gzip, deflate, br',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Cache-Control': 'max-age=0',
            'Connection': 'keep-alive',
            'Host': 'login-sso.siyscrm.com',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
        }
        
        try:
            response = self.session.get(login_page_url, headers=headers)
            self.log_request_details("GET", login_page_url, headers, None, response)
            
            if response.status_code == 200:
                print("✅ 登录页面访问成功")
                return True
            else:
                print(f"❌ 登录页面访问失败: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 登录页面访问异常: {e}")
            return False
    
    def test_step_2_captcha_key(self):
        """测试步骤2：获取验证码key"""
        print("\n[测试步骤2] 获取验证码key")
        
        key_url = f"{self.base_url}/Authorize/GetImgVerifyCodeKey"
        headers = {
            'Accept': '*/*',
            'Accept-Encoding': 'gzip, deflate, br',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Connection': 'keep-alive',
            'Host': 'login-sso.siyscrm.com',
            'X-Requested-With': 'XMLHttpRequest',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
        }
        
        try:
            response = self.session.get(key_url, headers=headers)
            self.log_request_details("GET", key_url, headers, None, response)
            
            if response.status_code == 200:
                img_code_key = response.json()
                print(f"✅ 验证码key获取成功: {img_code_key}")
                return img_code_key
            else:
                print(f"❌ 验证码key获取失败: {response.status_code}")
                return None
        except Exception as e:
            print(f"❌ 验证码key获取异常: {e}")
            return None
    
    def test_step_3_captcha_image(self, img_code_key):
        """测试步骤3：下载验证码图片"""
        print("\n[测试步骤3] 下载验证码图片")
        
        img_url = f"{self.base_url}/Authorize/GetImgVerifyCode?key={img_code_key}"
        headers = {
            'Accept': '*/*',
            'Accept-Encoding': 'gzip, deflate, br',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Connection': 'keep-alive',
            'Host': 'login-sso.siyscrm.com',
            'X-Requested-With': 'XMLHttpRequest',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
        }
        
        try:
            response = self.session.get(img_url, headers=headers)
            self.log_request_details("GET", img_url, headers, None, response)
            
            if response.status_code == 200:
                captcha_filename = f'captcha_diagnosis_{int(time.time())}.jpg'
                with open(captcha_filename, 'wb') as f:
                    f.write(response.content)
                print(f"✅ 验证码图片下载成功: {captcha_filename}")
                
                # 尝试自动识别验证码
                captcha_code = None
                if self.ocr:
                    captcha_code = self.recognize_captcha(captcha_filename)
                
                if not captcha_code:
                    print(f"请查看验证码图片: {captcha_filename}")
                    captcha_code = input("请输入验证码: ").strip()
                
                return captcha_code, captcha_filename
            else:
                print(f"❌ 验证码图片下载失败: {response.status_code}")
                return None, None
        except Exception as e:
            print(f"❌ 验证码图片下载异常: {e}")
            return None, None

    def test_step_4_login_request(self, img_code, img_code_key):
        """测试步骤4：发送登录请求（修正版本）"""
        print("\n[测试步骤4] 发送登录请求")

        # 生成access_code（尝试多种算法）
        algorithms = [
            ("password + client_id + username", hashlib.md5((self.password + self.client_id + self.username).encode('utf-8')).hexdigest().upper()),
            ("username + password + client_id", hashlib.md5((self.username + self.password + self.client_id).encode('utf-8')).hexdigest().upper()),
            ("client_id + username + password", hashlib.md5((self.client_id + self.username + self.password).encode('utf-8')).hexdigest().upper()),
        ]

        timestamp = int(time.time() * 1000)

        for algo_name, access_code in algorithms:
            print(f"\n--- 尝试算法: {algo_name} ---")
            print(f"Access Code: {access_code}")

            # 构建token
            token = self.build_token_corrected(self.username, self.password, self.client_id, self.redirect_uri, access_code, timestamp)

            # 验证token解码
            self.fix_token_decode(token)

            # 测试两种请求体格式
            payloads = [
                {
                    'name': '',
                    'pwd': '',
                    'redirect_uri': '',
                    'client_id': '',
                    'img_verify_code': img_code,
                    'img_verify_code_key': img_code_key,
                    'is_mobile_verify': False
                },
                {
                    'name': self.username,
                    'pwd': self.password,
                    'redirect_uri': self.redirect_uri,
                    'client_id': self.client_id,
                    'img_verify_code': img_code,
                    'img_verify_code_key': img_code_key,
                    'is_mobile_verify': False
                }
            ]

            for i, payload in enumerate(payloads, 1):
                print(f"\n--- 请求体格式 {i} ---")

                login_url = f"{self.base_url}/Authorize/postV2"
                headers = {
                    'Accept': 'application/json, text/javascript, */*; q=0.01',
                    'Accept-Encoding': 'gzip, deflate, br',
                    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                    'Connection': 'keep-alive',
                    'Content-Type': 'application/json',
                    'Host': 'login-sso.siyscrm.com',
                    'Origin': 'https://login-sso.siyscrm.com',
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                    'X-Requested-With': 'XMLHttpRequest',
                    'token': token
                }

                try:
                    response = self.session.post(login_url, headers=headers, json=payload, allow_redirects=False)
                    self.log_request_details("POST", login_url, headers, payload, response)

                    # 分析响应
                    success = self.analyze_login_response(response, algo_name, i)
                    if success:
                        return True

                except Exception as e:
                    print(f"❌ 登录请求异常: {e}")
                    logging.error(f"登录请求异常: {e}")

        return False

    def analyze_login_response(self, response, algo_name, payload_type):
        """分析登录响应"""
        print(f"\n=== 响应分析 (算法: {algo_name}, 请求体: {payload_type}) ===")

        if response.status_code == 200:
            try:
                result = response.json()
                print(f"JSON响应: {json.dumps(result, indent=2, ensure_ascii=False)}")

                if result.get("Status") == 0:
                    print("✅ 登录成功!")
                    if result.get("Result"):
                        print(f"重定向URL: {result['Result']}")
                    return True
                elif result.get("Status") == 2 and "验证码" in result.get("Message", ""):
                    print("⚠️ 验证码错误，但HTTP请求方法正确")
                    return False
                else:
                    print(f"❌ 登录失败: {result.get('Message', '未知错误')}")
                    return False

            except json.JSONDecodeError:
                print(f"非JSON响应: {response.text[:200]}")
                return False

        elif response.status_code in [301, 302, 303, 307, 308]:
            location = response.headers.get('Location', '')
            print(f"✅ 重定向响应: {location}")
            return True
        else:
            print(f"❌ 响应状态码: {response.status_code}")
            print(f"响应内容: {response.text[:200]}")
            return False

    def run_diagnosis(self):
        """运行完整的诊断测试"""
        print("=" * 60)
        print("HTTP登录方案诊断测试开始")
        print("=" * 60)

        # 步骤1：访问登录页面
        if not self.test_step_1_login_page():
            print("❌ 诊断终止：登录页面访问失败")
            return False

        # 步骤2：获取验证码key
        img_code_key = self.test_step_2_captcha_key()
        if not img_code_key:
            print("❌ 诊断终止：验证码key获取失败")
            return False

        # 步骤3：下载验证码图片
        img_code, captcha_filename = self.test_step_3_captcha_image(img_code_key)
        if not img_code:
            print("❌ 诊断终止：验证码获取失败")
            return False

        # 步骤4：发送登录请求
        success = self.test_step_4_login_request(img_code, img_code_key)

        # 清理验证码文件
        try:
            if captcha_filename and os.path.exists(captcha_filename):
                os.remove(captcha_filename)
                print(f"已清理验证码文件: {captcha_filename}")
        except:
            pass

        print("=" * 60)
        if success:
            print("✅ 诊断完成：找到可工作的登录方案")
        else:
            print("❌ 诊断完成：所有测试方案均失败")
            print("建议：使用Chrome MCP工具进行进一步分析")
        print("=" * 60)

        return success

def main():
    """主函数"""
    print("登录方案诊断测试工具")
    print("此工具将测试HTTP登录方案的每个步骤，并提供详细的调试信息")
    print("建议配合Chrome MCP工具使用以获得更详细的网络请求分析")
    print()

    tester = LoginDiagnosisTester()
    tester.run_diagnosis()

if __name__ == "__main__":
    main()
